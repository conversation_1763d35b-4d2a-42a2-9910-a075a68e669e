# FraudShield Environment Configuration
# Copy this file to .env and update the values as needed

# Database Configuration
DATABASE_URL=sqlite:///fraudshield.db

# Email Configuration (for notifications)
EMAIL_USERNAME=STREET-cBOY
EMAIL_PASSWORD=0710puqp@PUQP
FROM_EMAIL=<EMAIL>
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587

# API Keys
API_GATEWAY_KEY=fs_gateway_key_123
FRONTEND_API_KEY=fs_frontend_key_123
ADMIN_API_KEY=fs_admin_key_456
TEST_API_KEY=fs_test_key_789

# Service URLs (usually don't need to change for local development)
MODEL_SERVICE_URL=http://localhost:8001
INGEST_SERVICE_URL=http://localhost:9001
ANALYTICS_SERVICE_URL=http://localhost:8002
DATABASE_SERVICE_URL=http://localhost:8003
NOTIFICATION_SERVICE_URL=http://localhost:8004
BATCH_SERVICE_URL=http://localhost:8005
MONITORING_SERVICE_URL=http://localhost:8006
API_GATEWAY_URL=http://localhost:8000

# Monitoring Configuration
MONITORING_INTERVAL=30
ALERT_CPU_THRESHOLD=80
ALERT_MEMORY_THRESHOLD=85
ALERT_DISK_THRESHOLD=90
ALERT_RESPONSE_TIME_THRESHOLD=5.0

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# Batch Processing
MAX_BATCH_SIZE=1000
BATCH_TIMEOUT=300

# Model Configuration
MODEL_PATH=fraud_detection_model.pkl
MODEL_VERSION=1.0.0

# Logging
LOG_LEVEL=INFO
LOG_FILE=fraudshield.log

# Security
SECRET_KEY=your_secret_key_here
JWT_SECRET_KEY=your_jwt_secret_key_here

# Development Settings
DEBUG=False
TESTING=False
